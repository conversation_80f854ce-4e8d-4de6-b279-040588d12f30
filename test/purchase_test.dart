import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:banana/controller/purchase_controller.dart';
import 'package:banana/core/services/purchase_service.dart';
import 'package:banana/model/subscription_model.dart';

void main() {
  group('Purchase Integration Tests', () {
    late PurchaseController purchaseController;
    late PurchaseService purchaseService;

    setUp(() {
      // Initialize GetX for testing
      Get.testMode = true;

      // Initialize purchase controller without service dependency for testing
      purchaseController = PurchaseController();

      // Manually set available plans for testing
      purchaseController.availablePlans.value = SubscriptionPlans.getDefaultPlans();
    });

    tearDown(() {
      Get.reset();
    });

    test('should initialize with default subscription plans', () {
      expect(purchaseController.availablePlans.length, equals(3));
      expect(purchaseController.selectedPlan.value, equals(SubscriptionPlans.yearlyId));
    });

    test('should have correct product IDs', () {
      expect(SubscriptionPlans.weeklyId, equals('banana_premium_weekly'));
      expect(SubscriptionPlans.monthlyId, equals('banana_premium_monthly'));
      expect(SubscriptionPlans.yearlyId, equals('banana_premium_yearly'));
    });

    test('should select plan correctly', () {
      purchaseController.selectPlan(SubscriptionPlans.monthlyId);
      expect(purchaseController.selectedPlan.value, equals(SubscriptionPlans.monthlyId));
    });

    test('should get plan details correctly', () {
      final weeklyPlan = SubscriptionPlans.getPlanById(SubscriptionPlans.weeklyId);
      expect(weeklyPlan, isNotNull);
      expect(weeklyPlan!.id, equals(SubscriptionPlans.weeklyId));
      expect(weeklyPlan.period, equals('Per Week'));
    });

    test('should get selected plan correctly', () {
      purchaseController.selectPlan(SubscriptionPlans.yearlyId);
      final selectedPlan = purchaseController.getSelectedPlan();
      expect(selectedPlan, isNotNull);
      expect(selectedPlan!.id, equals(SubscriptionPlans.yearlyId));
      expect(selectedPlan.isPopular, isTrue);
    });

    test('should return correct plan display information', () {
      final label = purchaseController.getPlanDisplayLabel(SubscriptionPlans.yearlyId);
      final period = purchaseController.getPlanDisplayPeriod(SubscriptionPlans.yearlyId);
      final isPopular = purchaseController.isPlanPopular(SubscriptionPlans.yearlyId);
      
      expect(label, equals('Most Popular'));
      expect(period, equals('Per Year'));
      expect(isPopular, isTrue);
    });

    test('should handle subscription status correctly', () {
      final status = SubscriptionStatus(
        isActive: true,
        currentPlan: SubscriptionPlans.yearlyId,
        status: 'active',
      );
      
      expect(status.isActive, isTrue);
      expect(status.currentPlan, equals(SubscriptionPlans.yearlyId));
      expect(status.status, equals('active'));
    });

    test('should create purchase result correctly', () {
      final successResult = PurchaseResult.success(
        transactionId: 'test_transaction',
        productId: SubscriptionPlans.yearlyId,
        purchaseDate: DateTime.now(),
      );
      
      final errorResult = PurchaseResult.error('Test error');
      
      expect(successResult.success, isTrue);
      expect(successResult.productId, equals(SubscriptionPlans.yearlyId));
      expect(errorResult.success, isFalse);
      expect(errorResult.error, equals('Test error'));
    });

    test('should serialize subscription plan to JSON correctly', () {
      final plan = SubscriptionPlans.getDefaultPlans().first;
      final json = plan.toJson();
      
      expect(json['id'], equals(plan.id));
      expect(json['name'], equals(plan.name));
      expect(json['price'], equals(plan.price));
      expect(json['features'], equals(plan.features));
    });

    test('should deserialize subscription plan from JSON correctly', () {
      final json = {
        'id': 'test_plan',
        'name': 'Test Plan',
        'description': 'Test Description',
        'price': '\$9.99',
        'period': 'Per Month',
        'label': 'Test Label',
        'is_popular': true,
        'features': ['Feature 1', 'Feature 2'],
      };
      
      final plan = SubscriptionPlan.fromJson(json);
      
      expect(plan.id, equals('test_plan'));
      expect(plan.name, equals('Test Plan'));
      expect(plan.price, equals('\$9.99'));
      expect(plan.isPopular, isTrue);
      expect(plan.features.length, equals(2));
    });

    test('should handle subscription status serialization', () {
      final status = SubscriptionStatus(
        isActive: true,
        currentPlan: SubscriptionPlans.yearlyId,
        expiryDate: DateTime(2024, 12, 31),
        purchaseDate: DateTime(2024, 1, 1),
        isInTrial: false,
        remainingDays: 365,
        status: 'active',
      );
      
      final json = status.toJson();
      final deserializedStatus = SubscriptionStatus.fromJson(json);
      
      expect(deserializedStatus.isActive, equals(status.isActive));
      expect(deserializedStatus.currentPlan, equals(status.currentPlan));
      expect(deserializedStatus.status, equals(status.status));
      expect(deserializedStatus.remainingDays, equals(status.remainingDays));
    });
  });

  group('Purchase Service Constants Tests', () {
    test('should have correct product IDs defined', () {
      expect(PurchaseService.weeklyProductId, equals('banana_premium_weekly'));
      expect(PurchaseService.monthlyProductId, equals('banana_premium_monthly'));
      expect(PurchaseService.yearlyProductId, equals('banana_premium_yearly'));
    });
  });
}
