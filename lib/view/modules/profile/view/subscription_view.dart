import 'package:banana/core/constants/assets_constants.dart';
import 'package:banana/core/constants/color_constants.dart';
import 'package:banana/core/widgets/custom_button.dart';
import 'package:banana/core/widgets/widgets.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../core/widgets/text_widgets.dart';
import '../../../../controller/purchase_controller.dart';
import '../../../../model/subscription_model.dart';

class SubscriptionView extends StatelessWidget {
  final controller = Get.put(PremiumController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: // Close button and illustration
            Padding(
          padding: const EdgeInsets.all(15),
          child: InkWell(
              onTap: () {
                Get.back();
              },
              child: Icon(Icons.close)),
        ),
      ),
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(15),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Expanded(
                child: Image.asset(
                  Assets.paymentIllust, // Replace with your asset
                  height: 200,
                ),
              ),

              Widgets.heightSpaceH4,
              Stack(
                children: [
                  Container(
                    padding: EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      color: ColorConstants.backgroundColor,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.black, width: 1),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        featureRow('Send 5 Ask-Out Requests'),
                        featureRow('Join 3 Events'),
                        featureRow('Priority Request Visibility'),
                      ],
                    ),
                  ),Positioned(

                      right: 30,
                      child:Image.asset(Assets.crownIcon,width: 40,height: 40,))
                ],
              ),

              Widgets.heightSpaceH2,

              // Subscription Options
              Obx(() => Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      buildPlanOption(
                        label: 'Save 30%',
                        price: '\$4.99',
                        period: 'Per Week',
                        value: 'week',
                        controller: controller,
                      ),
                      buildPlanOption(
                        label: 'Most Popular',
                        price: '\$99.99',
                        period: 'Per Year',
                        value: 'year',
                        controller: controller,
                        highlight: true,
                      ),
                      buildPlanOption(
                        label: 'Save 50%',
                        price: '\$14.99',
                        period: 'Per Month',
                        value: 'month',
                        controller: controller,
                      ),
                    ],
                  )),

              Widgets.heightSpaceH5,

              // Info Text
              Column(
                children: [
                  Texts.textBold(
                      "Go Bananas Premium Packages – Unlock More Connections!",
                      maxline: 3,size: 19,
                      align: TextAlign.center),
                  SizedBox(height: 5),
                  Texts.textNormal(
                      "Choose a plan and get credits to send Ask-Outs & Event Requests!",
                      align: TextAlign.center,
                      size: 10,
                      color: Colors.black54),
                ],
              ),

              Widgets.heightSpaceH3,

              CustomButton(
                label: "Continue",
                onTap: () {},
              ),    Widgets.heightSpaceH1,
            ],
          ),
        ),
      ),
    );
  }

  Widget featureRow(String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            Icons.check,
            color: Colors.green,
            size: 15,
          ),
          SizedBox(width: 8),
          Expanded(child: Texts.textBlock(text, size: 12)),
        ],
      ),
    );
  }

  Widget buildPlanOption({
    required String label,
    required String price,
    required String period,
    required String value,
    required PremiumController controller,
    bool highlight = false,
  }) {
    bool isSelected = controller.selectedPlan.value == value;

    return GestureDetector(
      onTap: () => controller.selectPlan(value),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Container(
          width: 100,
          decoration: BoxDecoration(
            color: isSelected
                ? ColorConstants.lightPrimaryColor
                : ColorConstants.backgroundColor,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isSelected
                  ? ColorConstants.primaryColor
                  : ColorConstants.backgroundColor,
              width: 1,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                  width: 100,
                  padding: EdgeInsets.symmetric(horizontal: 4,vertical: 7),
                  decoration: BoxDecoration(color: isSelected
                      ? ColorConstants.primaryColor
                      : ColorConstants.blackColor),
                  alignment: Alignment.center,
                  child: Texts.textSemiBold(
                    label,
                    size: 11,
                    color: isSelected ? Colors.black : Colors.white,
                  )),
             Widgets.heightSpaceH2,
              Texts.textBold(price,size: 17
                  ),    Widgets.heightSpaceH05,
              Texts.textNormal(period, size: 9,color: Colors.black54),

              Widgets.heightSpaceH2
            ],
          ),
        ),
      ),
    );
  }
}
