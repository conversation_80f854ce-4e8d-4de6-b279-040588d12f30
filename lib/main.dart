import 'dart:async';
import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:banana/constants/my_colors.dart';
import 'package:banana/view/starting/view/splash_view.dart';
import 'package:banana/utils/Utility.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get_navigation/src/root/get_material_app.dart';
import 'package:get/get_navigation/src/routes/transitions_type.dart';
import 'package:get/get.dart';

import 'core/constants/color_constants.dart';
import 'core/services/purchase_service.dart';
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();
  AwesomeNotifications().createNotificationFromJsonData(message.data);

  print('Handling a background message: ${message.messageId}');
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
  ));
  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);
  await Firebase.initializeApp();

  AwesomeNotifications().initialize(
    // Set the icon and configure channels in this method
      null, // Use default icon or specify a custom one
      [
        NotificationChannel(
          channelKey: 'default_channel_id',
          channelName: 'Basic notifications',
          channelDescription: 'Notification channel for basic tests',
          defaultColor: ColorConstants.primaryColor,
          ledColor: Colors.white,
          importance: NotificationImportance.High,
        ),
      ],
      debug: false);

  AwesomeNotifications().isNotificationAllowed().then((isAllowed) {
    if (!isAllowed) {
      AwesomeNotifications().requestPermissionToSendNotifications();
    }
  });
  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
  runApp(const MyApp());
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {


  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(360, 690),
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (_, child) {
        return GetMaterialApp(
          builder: EasyLoading.init(),
          defaultTransition: Transition.rightToLeft,
          debugShowCheckedModeBanner: false,
          theme: ThemeData(
            fontFamily: "sansRegular",appBarTheme:  const AppBarTheme(
              backgroundColor: Colors.white,shadowColor: Colors.white,scrolledUnderElevation:0,
            ),
            scaffoldBackgroundColor: Colors.white,
          ),
          home: child,
        );
      },
      child: SplashView(),
    );
  }
}
